"""
YouTube数据抓取器

基于Playwright实现YouTube Studio数据抓取功能，包括：
1. 自动登录YouTube Studio
2. 导航到Shorts页面
3. 抓取视频数据（标题、观看量、评论数、点赞数、点赞比例）
4. 分页处理
5. 数据过滤和清洗
"""

import asyncio
import re
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
import logging

try:
    from ..browser.base import BrowserManager, BrowserInstance, BrowserStatus
    from ...models.profile import Profile
    from ...utils.logger import get_logger, TaskLogger
except ImportError:
    # 处理测试环境的导入
    from core.browser.base import BrowserManager, BrowserInstance
    from models.profile import Profile
    from utils.logger import get_logger, TaskLogger


class YouTubeVideoData:
    """YouTube视频数据模型"""
    
    def __init__(self, title: str, views: int, comments: int, likes: int,
                 like_ratio: float, upload_date: Optional[datetime] = None, account: str = ""):
        """
        初始化视频数据

        Args:
            title: 视频标题
            views: 观看量
            comments: 评论数
            likes: 点赞数
            like_ratio: 点赞比例
            upload_date: 上传日期
            account: 发布账号
        """
        self.title = title
        self.views = views
        self.comments = comments
        self.likes = likes
        self.like_ratio = like_ratio
        self.upload_date = upload_date
        self.account = account
        self.scraped_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'title': self.title,
            'views': self.views,
            'comments': self.comments,
            'likes': self.likes,
            'like_ratio': self.like_ratio,
            'upload_date': self.upload_date.isoformat() if self.upload_date else None,
            'account': self.account,
            'scraped_at': self.scraped_at.isoformat()
        }


class YouTubeScraper:
    """YouTube数据抓取器"""
    
    def __init__(self, browser_manager: BrowserManager, config: Dict[str, Any]):
        """
        初始化抓取器

        Args:
            browser_manager: 浏览器管理器
            config: 配置信息
        """
        self.browser_manager = browser_manager
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        # 临时设置为DEBUG级别以便调试选择器
        self.logger.setLevel(logging.DEBUG)

        # 确保根日志器也是DEBUG级别
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)

        # 确保所有处理器都是DEBUG级别
        for handler in root_logger.handlers:
            handler.setLevel(logging.DEBUG)

        # Playwright相关对象
        self.playwright = None
        self.browser = None
        self.context = None
        
        # 抓取配置
        self.page_wait_timeout = config.get('page_wait_timeout', 30000)  # 30秒
        self.element_wait_timeout = config.get('element_wait_timeout', 10000)  # 10秒
        self.scroll_delay = config.get('scroll_delay', 2000)  # 2秒
        self.max_retries = config.get('max_retries', 3)

        # 选择器配置 - 从配置文件读取，如果没有则使用默认值
        data_extraction = config.get('data_extraction', {})
        self.selectors = {
            'content_menu': data_extraction.get('content_menu_selector', 'tp-yt-paper-icon-item.videos'),
            'shorts_tab': data_extraction.get('shorts_tab_selector', 'tp-yt-paper-tab#video-list-shorts-tab'),
            'video_rows': data_extraction.get('video_row_selector', 'ytcp-video-row[role="row"]'),
            'video_title': data_extraction.get('title_selector', '#video-title > span'),
            'video_views': data_extraction.get('views_selector', 'div.tablecell-views'),
            'video_comments': data_extraction.get('comments_selector', 'div.tablecell-comments'),
            'video_likes': data_extraction.get('likes_selector', 'div.tablecell-likes .likes-label'),
            'video_like_percent': data_extraction.get('like_ratio_selector', 'div.tablecell-likes .percent-label'),
            'next_page_button': data_extraction.get('next_page_selector', 'ytcp-icon-button#navigate-after')
        }
    
    async def scrape_profile_data(self, profile: Profile, 
                                 progress_callback: Optional[Callable] = None) -> List[YouTubeVideoData]:
        """
        抓取指定Profile的数据
        
        Args:
            profile: Profile配置
            progress_callback: 进度回调函数
            
        Returns:
            List[YouTubeVideoData]: 抓取到的视频数据列表
        """
        task_logger = TaskLogger(f"scrape-{profile.id}", profile.name)
        task_logger.task_start()
        
        try:
            # 打开浏览器
            browser_instance = await self._open_browser(profile)
            if not browser_instance:
                raise Exception("无法打开浏览器")
            
            task_logger.step("浏览器启动", "浏览器启动成功")
            
            # 连接到页面
            page = await self._connect_to_page(browser_instance)
            if not page:
                raise Exception("无法连接到页面")
            
            task_logger.step("页面连接", "页面连接成功")
            
            # 导航到YouTube Studio
            await self._navigate_to_youtube_studio(page)
            task_logger.step("页面导航", "导航到YouTube Studio成功")
            
            # 等待页面加载并点击内容菜单
            await self._click_content_menu(page)
            task_logger.step("内容菜单", "点击内容菜单成功")
            
            # 点击Shorts标签
            await self._click_shorts_tab(page)
            task_logger.step("Shorts标签", "点击Shorts标签成功")
            
            # 抓取视频数据
            videos_data = await self._scrape_videos_data(page, profile, progress_callback, task_logger)
            
            task_logger.step("数据抓取", f"成功抓取 {len(videos_data)} 个视频数据")
            task_logger.task_complete(True, f"抓取完成，共 {len(videos_data)} 个视频")
            
            return videos_data
            
        except Exception as e:
            error_msg = f"抓取Profile数据失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            task_logger.task_complete(False, error_msg)
            raise
        
        finally:
            # TODO 要修改回来，临时测试
            pass
            # 清理资源
            # try:
            #     # 清理Playwright资源
            #     await self._cleanup_playwright()

            #     # 关闭浏览器
            #     if 'browser_instance' in locals():
            #         await self._close_browser(browser_instance)
            # except Exception as e:
            #     self.logger.warning(f"清理资源时出错: {str(e)}")
            
    
    async def _open_browser(self, profile: Profile) -> Optional[BrowserInstance]:
        """打开浏览器"""
        try:
            browser_instance = await self.browser_manager.open_browser(profile.browser_window_id)
            if browser_instance and browser_instance.status.value == BrowserStatus.OPEN.value:
                return browser_instance
            else:
                self.logger.error(f"浏览器启动失败: {profile.browser_window_id}")
                return None
        except Exception as e:
            self.logger.error(f"打开浏览器失败: {str(e)}")
            return None
    
    async def _close_browser(self, browser_instance: BrowserInstance):
        """关闭浏览器"""
        try:
            await self.browser_manager.close_browser(browser_instance)
        except Exception as e:
            self.logger.warning(f"关闭浏览器失败: {str(e)}")

    async def _cleanup_playwright(self):
        """清理Playwright资源"""
        try:
            if self.context:
                await self.context.close()
                self.context = None
            if self.browser:
                await self.browser.close()
                self.browser = None
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
        except Exception as e:
            self.logger.warning(f"清理Playwright资源失败: {str(e)}")
    
    async def _connect_to_page(self, browser_instance: BrowserInstance) -> Optional[Page]:
        """连接到浏览器页面"""
        try:
            # 使用Playwright连接到现有浏览器
            self.playwright = await async_playwright().start()

            # 连接到远程浏览器
            self.browser = await self.playwright.chromium.connect_over_cdp(browser_instance.ws_endpoint)

            # 获取现有上下文或创建新上下文
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
            else:
                self.context = await self.browser.new_context()

            # 创建新页面
            page = await self.context.new_page()

            return page

        except Exception as e:
            self.logger.error(f"连接到页面失败: {str(e)}")
            return None
    
    async def _navigate_to_youtube_studio(self, page: Page):
        """导航到YouTube Studio"""
        try:
            self.logger.info("导航到YouTube Studio...")
            # 使用domcontentloaded而不是networkidle，更适合SPA应用
            await page.goto("https://studio.youtube.com", wait_until="domcontentloaded", timeout=self.page_wait_timeout)
            self.logger.info("页面导航完成，等待关键元素加载...")

            # 等待关键元素出现，而不是等待网络空闲
            try:
                # 等待内容菜单出现，这表明页面主要部分已加载
                await page.locator(self.selectors['content_menu']).first.wait_for(timeout=15000)
                self.logger.info("YouTube Studio关键元素加载完成")
            except Exception as element_e:
                self.logger.warning(f"等待关键元素超时，但继续执行: {str(element_e)}")
                # 给页面更多时间
                await asyncio.sleep(5)

            self.logger.info("YouTube Studio页面加载完成")

        except Exception as e:
            raise Exception(f"导航到YouTube Studio失败: {str(e)}")
    
    async def _click_content_menu(self, page: Page):
        """点击内容菜单"""
        try:
            self.logger.info("等待内容菜单出现...")
            # 等待内容菜单出现
            await page.locator(self.selectors['content_menu']).first.wait_for(timeout=self.element_wait_timeout)
            self.logger.info("内容菜单已出现")

            # 点击内容菜单
            self.logger.info("点击内容菜单...")
            await page.click(self.selectors['content_menu'])
            self.logger.info("内容菜单点击成功")

            # 等待内容页面的关键元素出现，而不是等待网络空闲
            self.logger.info("等待内容页面元素加载...")
            try:
                # 等待Shorts标签出现，这表明内容菜单点击成功并且页面已加载
                await page.locator(self.selectors['shorts_tab']).first.wait_for(timeout=10000)
                self.logger.info("内容页面加载完成")
            except Exception as load_e:
                self.logger.warning(f"等待内容页面元素超时，但继续执行: {str(load_e)}")

            await asyncio.sleep(1)  # 减少等待时间

        except Exception as e:
            if "wait_for" in str(e):
                raise Exception(f"等待内容菜单出现失败: {str(e)}")
            elif "click" in str(e):
                raise Exception(f"点击内容菜单失败: {str(e)}")
            else:
                raise Exception(f"内容菜单操作失败: {str(e)}")
    
    async def _click_shorts_tab(self, page: Page):
        """点击Shorts标签"""
        try:
            self.logger.info("等待Shorts标签出现...")
            # 等待Shorts标签出现
            await page.locator(self.selectors['shorts_tab']).first.wait_for(timeout=self.element_wait_timeout)
            self.logger.info("Shorts标签已出现")

            # 点击Shorts标签
            self.logger.info("点击Shorts标签...")
            await page.click(self.selectors['shorts_tab'])
            self.logger.info("Shorts标签点击成功")

            # 等待Shorts页面的视频列表加载
            self.logger.info("等待Shorts视频列表加载...")
            try:
                # 等待第一个视频行出现，这表明Shorts页面已经加载完成
                await page.locator(self.selectors['video_rows']).first.wait_for(timeout=10000)
                self.logger.info("Shorts视频列表加载完成")
            except Exception as load_e:
                self.logger.warning(f"等待Shorts视频列表超时，但继续执行: {str(load_e)}")

            await asyncio.sleep(2)  # 减少等待时间

        except Exception as e:
            if "wait_for" in str(e):
                raise Exception(f"等待Shorts标签出现失败: {str(e)}")
            elif "click" in str(e):
                raise Exception(f"点击Shorts标签失败: {str(e)}")
            else:
                raise Exception(f"Shorts标签操作失败: {str(e)}")
    
    async def _scrape_videos_data(self, page: Page, profile: Profile, 
                                 progress_callback: Optional[Callable], 
                                 task_logger: TaskLogger) -> List[YouTubeVideoData]:
        """抓取视频数据"""
        all_videos = []
        page_number = 1
        
        # 解析最早日期
        earliest_date = None
        if profile.earliest_date:
            try:
                earliest_date = datetime.strptime(profile.earliest_date, "%Y-%m-%d")
            except ValueError:
                self.logger.warning(f"无效的最早日期格式: {profile.earliest_date}")
        
        while True:
            try:
                task_logger.step(f"第{page_number}页", f"开始抓取第{page_number}页数据")
                
                # 等待视频列表加载
                await page.locator(self.selectors['video_rows']).first.wait_for(timeout=self.element_wait_timeout)
                
                # 获取当前页面的视频数据
                page_videos = await self._extract_videos_from_page(page, earliest_date, profile.youtube_account)
                
                if not page_videos:
                    task_logger.step(f"第{page_number}页", "没有找到视频数据，停止抓取")
                    break
                
                all_videos.extend(page_videos)
                task_logger.step(f"第{page_number}页", f"抓取到 {len(page_videos)} 个视频")
                
                # 更新进度
                if progress_callback:
                    progress_callback(f"已抓取 {len(all_videos)} 个视频 (第{page_number}页)")
                
                # 检查是否有下一页
                has_next_page = await self._has_next_page(page)
                if not has_next_page:
                    task_logger.step("分页检查", "没有更多页面，抓取完成")
                    break
                
                # 点击下一页
                await self._click_next_page(page)
                page_number += 1
                
                # 等待页面加载
                await asyncio.sleep(self.scroll_delay / 1000)
                
            except Exception as e:
                self.logger.error(f"抓取第{page_number}页数据失败: {str(e)}")
                break

        return all_videos
    
    async def _extract_videos_from_page(self, page: Page, earliest_date: Optional[datetime], account: str = "") -> List[YouTubeVideoData]:
        """从当前页面提取视频数据"""
        videos = []
        
        try:
            # 获取所有视频行
            self.logger.info("开始获取视频行...")
            video_rows = await page.locator(self.selectors['video_rows']).all()
            self.logger.info(f"找到 {len(video_rows)} 个视频行")

            # 调试：输出第一个视频行的HTML结构和选择器测试
            # if video_rows:
            #     try:
            #         first_row_html = await video_rows[0].inner_html()
            #         self.logger.debug(f"第一个视频行的HTML结构: {first_row_html[:800]}...")  # 显示前800字符

            #         # 测试各个选择器
            #         await self._debug_selectors(video_rows[0])

            #     except Exception as html_e:
            #         self.logger.warning(f"无法获取视频行HTML: {str(html_e)}")

            for i, row in enumerate(video_rows):
                try:
                    self.logger.debug(f"处理第 {i+1} 个视频行...")
                    video_data = await self._extract_video_data_from_row(row, account)
                    if video_data:
                        # 检查日期过滤
                        if earliest_date and video_data.upload_date and video_data.upload_date < earliest_date:
                            continue

                        videos.append(video_data)
                        self.logger.info(f"成功添加视频: {video_data.title}")
                    else:
                        self.logger.warning(f"第 {i+1} 个视频行数据提取失败")

                except Exception as e:
                    self.logger.warning(f"处理第 {i+1} 个视频行时出错: {str(e)}")
                    continue
            
        except Exception as e:
            self.logger.error(f"提取页面视频数据失败: {str(e)}")

        self.logger.info(f"本页共提取到 {len(videos)} 个有效视频")
        return videos

    async def _debug_selectors(self, row):
        """调试选择器，检查每个选择器是否能找到元素"""
        self.logger.debug("=== 开始调试选择器 ===")

        selectors_to_test = {
            'video_title': self.selectors['video_title'],
            'video_views': self.selectors['video_views'],
            'video_comments': self.selectors['video_comments'],
            'video_likes': self.selectors['video_likes']
        }

        for name, selector in selectors_to_test.items():
            try:
                locator = row.locator(selector)
                count = await locator.count()
                self.logger.debug(f"选择器 '{name}' ({selector}): 找到 {count} 个元素")

                if count > 0:
                    try:
                        text = await locator.first.inner_text()
                        self.logger.debug(f"  -> 第一个元素文本: '{text}'")
                    except Exception as text_e:
                        self.logger.debug(f"  -> 无法获取文本: {str(text_e)}")

                    try:
                        html = await locator.first.inner_html()
                        self.logger.debug(f"  -> 第一个元素HTML: {html[:200]}...")
                    except Exception as html_e:
                        self.logger.debug(f"  -> 无法获取HTML: {str(html_e)}")
                else:
                    # 尝试查找相似的选择器
                    self.logger.debug(f"  -> 尝试查找相似元素...")
                    try:
                        # 查找所有cell元素
                        cells = row.locator('[role="cell"]')
                        cell_count = await cells.count()
                        self.logger.debug(f"  -> 找到 {cell_count} 个 cell 元素")

                        for i in range(min(cell_count, 10)):  # 最多显示前10个
                            cell_text = await cells.nth(i).inner_text()
                            self.logger.debug(f"    Cell {i+1}: '{cell_text}'")

                    except Exception as cell_e:
                        self.logger.debug(f"  -> 查找cell元素失败: {str(cell_e)}")

            except Exception as e:
                self.logger.debug(f"选择器 '{name}' 测试失败: {str(e)}")

        self.logger.debug("=== 选择器调试结束 ===")
    
    async def _extract_video_data_from_row(self, row, account: str = "") -> Optional[YouTubeVideoData]:
        """从视频行提取数据"""
        try:
            self.logger.debug("开始提取视频行数据...")

            # 提取标题
            self.logger.debug(f"提取标题，选择器: {self.selectors['video_title']}")
            title_locator = row.locator(self.selectors['video_title'])
            title_count = await title_locator.count()
            self.logger.debug(f"标题元素数量: {title_count}")

            if title_count > 0:
                title = await title_locator.first.inner_text()
                self.logger.debug(f"提取到标题: {title}")
            else:
                title = "未知标题"
                self.logger.warning("未找到标题元素")

            # 提取观看量
            self.logger.debug(f"提取观看量，选择器: {self.selectors['video_views']}")
            views_locator = row.locator(self.selectors['video_views'])
            views_count = await views_locator.count()
            self.logger.debug(f"观看量元素数量: {views_count}")

            if views_count > 0:
                views_text = await views_locator.first.inner_text()
                self.logger.debug(f"提取到观看量文本: '{views_text}'")
                views = self._parse_number(views_text)
                self.logger.debug(f"解析后观看量: {views}")
            else:
                views_text = "0"
                views = 0
                self.logger.warning("未找到观看量元素")

            # 提取评论数
            self.logger.debug(f"提取评论数，选择器: {self.selectors['video_comments']}")
            comments_locator = row.locator(self.selectors['video_comments'])
            comments_count = await comments_locator.count()
            self.logger.debug(f"评论数元素数量: {comments_count}")

            if comments_count > 0:
                comments_text = await comments_locator.first.inner_text()
                self.logger.debug(f"提取到评论数文本: '{comments_text}'")
                comments = self._parse_number(comments_text)
                self.logger.debug(f"解析后评论数: {comments}")
            else:
                comments_text = "0"
                comments = 0
                self.logger.warning("未找到评论数元素")

            # 提取点赞数
            self.logger.debug(f"提取点赞数，选择器: {self.selectors['video_likes']}")
            likes_locator = row.locator(self.selectors['video_likes'])
            likes_count = await likes_locator.count()
            self.logger.debug(f"点赞数元素数量: {likes_count}")

            if likes_count > 0:
                likes_text = await likes_locator.first.inner_text()
                self.logger.debug(f"提取到点赞数文本: '{likes_text}'")

                if likes_text != '':
                    likes_text = likes_text.replace(' likes', '').replace(' 人赞', '')
                    likes = self._parse_number(likes_text)
                else:
                    likes = 0
                    self.logger.warning("点赞文本为空")
            else:
                likes = 0
                self.logger.warning("未找到点赞数元素")

            # 提取点赞比例
            self.logger.debug(f"提取点赞比例，选择器: {self.selectors['video_like_percent']}")
            like_percent_locator = row.locator(self.selectors['video_like_percent'])
            like_percent_count = await like_percent_locator.count()
            self.logger.debug(f"点赞比例元素数量: {like_percent_count}")

            if like_percent_count > 0:
                like_percent_text = await like_percent_locator.first.inner_text()
                self.logger.debug(f"提取到点赞比例文本: '{like_percent_text}'")

                if like_percent_text != '':
                    like_ratio = like_percent_text
                else:
                    like_ratio = '0%'
                    self.logger.warning("点赞比例文本为空")
            else:
                like_ratio = 0
                self.logger.warning("未找到点赞比例元素")

            video_data = YouTubeVideoData(
                title=title,
                views=views,
                comments=comments,
                likes=likes,
                like_ratio=like_ratio,
                account=account
            )

            self.logger.info(f"成功提取视频数据: 标题='{title}', 观看量={views}, 评论数={comments}, 点赞数={likes}, 点赞比例={like_ratio}")
            return video_data

        except Exception as e:
            self.logger.error(f"提取视频行数据失败: {str(e)}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return None
    
    def _parse_number(self, text: str) -> int:
        """解析数字文本"""
        try:
            # 移除逗号和空格
            clean_text = re.sub(r'[,\s]', '', text)
            
            # 处理K, M, B等单位
            if clean_text.endswith('K'):
                return int(float(clean_text[:-1]) * 1000)
            elif clean_text.endswith('M'):
                return int(float(clean_text[:-1]) * 1000000)
            elif clean_text.endswith('B'):
                return int(float(clean_text[:-1]) * **********)
            else:
                return int(clean_text) if clean_text.isdigit() else 0
                
        except (ValueError, AttributeError):
            return 0
    
    def _parse_percentage(self, text: str) -> float:
        """解析百分比文本"""
        try:
            # 移除%符号和空格
            clean_text = text.replace('%', '').strip()
            return float(clean_text) / 100.0
        except (ValueError, AttributeError):
            return 0.0
    
    async def _has_next_page(self, page: Page) -> bool:
        """检查是否有下一页"""
        try:
            next_button_locator = page.locator(self.selectors['next_page_button'])
            if await next_button_locator.count() > 0:
                is_disabled = await next_button_locator.first.get_attribute('disabled')
                return is_disabled is None
            return False
        except Exception:
            return False
    
    async def _click_next_page(self, page: Page):
        """点击下一页"""
        try:
            self.logger.info("点击下一页按钮...")
            await page.click(self.selectors['next_page_button'])
            self.logger.info("下一页按钮点击成功")

            # 等待新的视频行加载，而不是等待网络空闲
            self.logger.info("等待下一页内容加载...")
            try:
                # 等待页面内容更新，使用较短的等待时间
                await asyncio.sleep(3)  # 给页面一些时间来加载新内容

                # 验证是否有视频行存在
                video_rows_locator = page.locator(self.selectors['video_rows'])
                video_rows_count = await video_rows_locator.count()
                if video_rows_count > 0:
                    self.logger.info(f"下一页加载完成，发现 {video_rows_count} 个视频行")
                else:
                    self.logger.warning("下一页加载后未发现视频行")

            except Exception as wait_e:
                self.logger.warning(f"等待下一页内容时出现问题: {str(wait_e)}")

        except Exception as e:
            raise Exception(f"点击下一页失败: {str(e)}")


class YouTubeScrapingTask:
    """YouTube抓取任务"""

    def __init__(self, profile: Profile, scraper: YouTubeScraper):
        """
        初始化抓取任务

        Args:
            profile: Profile配置
            scraper: YouTube抓取器
        """
        self.profile = profile
        self.scraper = scraper
        self.status = "pending"  # pending, running, completed, failed
        self.progress = 0.0
        self.message = ""
        self.result: Optional[List[YouTubeVideoData]] = None
        self.error: Optional[str] = None
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None

    async def execute(self, progress_callback: Optional[Callable] = None) -> List[YouTubeVideoData]:
        """
        执行抓取任务

        Args:
            progress_callback: 进度回调函数

        Returns:
            List[YouTubeVideoData]: 抓取结果
        """
        self.status = "running"
        self.start_time = datetime.now()
        self.progress = 0.0
        self.message = "开始抓取..."

        try:
            # 包装进度回调
            def wrapped_progress_callback(message: str):
                self.message = message
                if progress_callback:
                    progress_callback(self, message)

            # 执行抓取
            self.result = await self.scraper.scrape_profile_data(
                self.profile,
                wrapped_progress_callback
            )

            self.status = "completed"
            self.progress = 1.0
            self.message = f"抓取完成，共 {len(self.result)} 个视频"
            self.end_time = datetime.now()

            return self.result

        except Exception as e:
            self.status = "failed"
            self.error = str(e)
            self.message = f"抓取失败: {str(e)}"
            self.end_time = datetime.now()
            raise

    def get_duration(self) -> Optional[timedelta]:
        """获取任务执行时长"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        elif self.start_time:
            return datetime.now() - self.start_time
        return None
