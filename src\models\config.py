"""
配置数据模型

定义应用程序的配置数据结构，提供配置验证和管理功能。
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
import json
import os
from pathlib import Path


@dataclass
class AppInfo:
    """应用程序信息"""
    name: str = "YouTube数据抓取应用"
    version: str = "1.0.0"
    author: str = "AI Assistant"
    description: str = "YouTube频道短视频数据自动抓取并同步到飞书多维表格的桌面应用"


@dataclass
class Settings:
    """应用程序设置"""
    window_width: int = 1200
    window_height: int = 800
    window_min_width: int = 800
    window_min_height: int = 600
    max_concurrent_tasks: int = 3
    retry_count: int = 3
    retry_delay: int = 2
    page_load_timeout: int = 30
    element_wait_timeout: int = 10
    log_level: str = "INFO"
    auto_save_interval: int = 300
    theme: str = "light"


@dataclass
class FeishuConfig:
    """飞书API配置"""
    app_id: str = ""
    app_secret: str = ""
    app_token: str = ""
    table_id_content: str = ""
    table_id_account: str = ""
    api_base_url: str = "https://open.feishu.cn/open-apis"
    field_mapping: Dict[str, str] = field(default_factory=lambda: {
        "title": "英文标题",
        "views": "播放量",
        "comments": "评论数",
        "likes": "赞数",
        "like_ratio": "赞比例",
        "account": "发布账号"
    })
    
    def is_configured(self) -> bool:
        """检查飞书配置是否完整"""
        return bool(self.app_id and self.app_secret and self.app_token and self.table_id_content)


@dataclass
class YouTubeDataExtraction:
    """YouTube数据提取选择器配置"""
    title_selector: str = "#video-title > span"
    views_selector: str = "div.tablecell-views"
    comments_selector: str = "div.tablecell-comments"
    likes_selector: str = "div.tablecell-likes .likes-label"
    like_ratio_selector: str = "div.tablecell-likes .percent-label"
    next_page_selector: str = "ytcp-icon-button#navigate-after"
    video_row_selector: str = "ytcp-video-row[role=\"row\"]"
    shorts_tab_selector: str = "tp-yt-paper-tab#video-list-shorts-tab"
    content_menu_selector: str = "tp-yt-paper-icon-item.videos"


@dataclass
class YouTubeConfig:
    """YouTube相关配置"""
    studio_url: str = "https://studio.youtube.com"
    earliest_date: str = ""
    data_extraction: YouTubeDataExtraction = field(default_factory=YouTubeDataExtraction)


@dataclass
class BrowserTypeConfig:
    """单个浏览器类型配置"""
    api_url: str
    timeout: int = 30


@dataclass
class BrowserConfig:
    """浏览器配置"""
    default_type: str = "bitbrowser"
    bitbrowser: BrowserTypeConfig = field(default_factory=lambda: BrowserTypeConfig("http://127.0.0.1:54345"))
    adspower: BrowserTypeConfig = field(default_factory=lambda: BrowserTypeConfig("http://127.0.0.1:50325"))


@dataclass
class LoggingConfig:
    """日志配置"""
    log_dir: str = "logs"
    max_log_files: int = 10
    max_log_size_mb: int = 10
    log_format: str = "[%(asctime)s] [%(levelname)s] [%(name)s] - %(message)s"
    date_format: str = "%Y-%m-%d %H:%M:%S"


@dataclass
class DataConfig:
    """数据存储配置"""
    profiles_file: str = "data/profiles.json"
    cache_dir: str = "data/cache"
    backup_dir: str = "data/backup"
    auto_backup: bool = True
    backup_interval_hours: int = 24


@dataclass
class AppConfig:
    """应用程序完整配置"""
    app: AppInfo = field(default_factory=AppInfo)
    settings: Settings = field(default_factory=Settings)
    feishu: FeishuConfig = field(default_factory=FeishuConfig)
    youtube: YouTubeConfig = field(default_factory=YouTubeConfig)
    browser: BrowserConfig = field(default_factory=BrowserConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    data: DataConfig = field(default_factory=DataConfig)
    
    @classmethod
    def load_from_file(cls, config_file: str = "config.json") -> "AppConfig":
        """
        从配置文件加载配置
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            AppConfig: 配置对象
            
        Raises:
            FileNotFoundError: 配置文件不存在
            json.JSONDecodeError: 配置文件格式错误
            ValueError: 配置验证失败
        """
        if not os.path.exists(config_file):
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return cls.from_dict(config_data)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AppConfig":
        """
        从字典创建配置对象
        
        Args:
            data: 配置数据字典
            
        Returns:
            AppConfig: 配置对象
        """
        # 创建各个子配置对象
        app_info = AppInfo(**data.get('app', {}))
        settings = Settings(**data.get('settings', {}))
        
        # 飞书配置
        feishu_data = data.get('feishu', {})
        feishu_config = FeishuConfig(
            app_id=feishu_data.get('app_id', ''),
            app_secret=feishu_data.get('app_secret', ''),
            app_token=feishu_data.get('app_token', ''),
            table_id_content=feishu_data.get('table_id_content', ''),
            table_id_account=feishu_data.get('table_id_account', ''),
            api_base_url=feishu_data.get('api_base_url', 'https://open.feishu.cn/open-apis'),
            field_mapping=feishu_data.get('field_mapping', {
                "title": "英文标题",
                "views": "播放量",
                "comments": "评论数",
                "likes": "赞数",
                "like_ratio": "赞比例",
                "account": "发布账号"
            })
        )
        
        # YouTube配置
        youtube_data = data.get('youtube', {})
        extraction_data = youtube_data.get('data_extraction', {})
        data_extraction = YouTubeDataExtraction(**extraction_data)
        youtube_config = YouTubeConfig(
            studio_url=youtube_data.get('studio_url', 'https://studio.youtube.com'),
            earliest_date=youtube_data.get('earliest_date', ''),
            data_extraction=data_extraction
        )
        
        # 浏览器配置
        browser_data = data.get('browser', {})
        bitbrowser_config = BrowserTypeConfig(**browser_data.get('bitbrowser', {'api_url': 'http://127.0.0.1:54345'}))
        adspower_config = BrowserTypeConfig(**browser_data.get('adspower', {'api_url': 'http://127.0.0.1:50325'}))
        browser_config = BrowserConfig(
            default_type=browser_data.get('default_type', 'bitbrowser'),
            bitbrowser=bitbrowser_config,
            adspower=adspower_config
        )
        
        # 日志配置
        logging_config = LoggingConfig(**data.get('logging', {}))
        
        # 数据配置
        data_config = DataConfig(**data.get('data', {}))
        
        return cls(
            app=app_info,
            settings=settings,
            feishu=feishu_config,
            youtube=youtube_config,
            browser=browser_config,
            logging=logging_config,
            data=data_config
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        return {
            'app': {
                'name': self.app.name,
                'version': self.app.version,
                'author': self.app.author,
                'description': self.app.description
            },
            'settings': {
                'window_width': self.settings.window_width,
                'window_height': self.settings.window_height,
                'window_min_width': self.settings.window_min_width,
                'window_min_height': self.settings.window_min_height,
                'max_concurrent_tasks': self.settings.max_concurrent_tasks,
                'retry_count': self.settings.retry_count,
                'retry_delay': self.settings.retry_delay,
                'page_load_timeout': self.settings.page_load_timeout,
                'element_wait_timeout': self.settings.element_wait_timeout,
                'log_level': self.settings.log_level,
                'auto_save_interval': self.settings.auto_save_interval,
                'theme': self.settings.theme
            },
            'feishu': {
                'app_id': self.feishu.app_id,
                'app_secret': self.feishu.app_secret,
                'app_token': self.feishu.app_token,
                'table_id_content': self.feishu.table_id_content,
                'table_id_account': self.feishu.table_id_account,
                'api_base_url': self.feishu.api_base_url,
                'field_mapping': self.feishu.field_mapping
            },
            'youtube': {
                'studio_url': self.youtube.studio_url,
                'earliest_date': self.youtube.earliest_date,
                'data_extraction': {
                    'title_selector': self.youtube.data_extraction.title_selector,
                    'views_selector': self.youtube.data_extraction.views_selector,
                    'comments_selector': self.youtube.data_extraction.comments_selector,
                    'likes_selector': self.youtube.data_extraction.likes_selector,
                    'like_ratio_selector': self.youtube.data_extraction.like_ratio_selector,
                    'next_page_selector': self.youtube.data_extraction.next_page_selector,
                    'video_row_selector': self.youtube.data_extraction.video_row_selector,
                    'shorts_tab_selector': self.youtube.data_extraction.shorts_tab_selector,
                    'content_menu_selector': self.youtube.data_extraction.content_menu_selector
                }
            },
            'browser': {
                'default_type': self.browser.default_type,
                'bitbrowser': {
                    'api_url': self.browser.bitbrowser.api_url,
                    'timeout': self.browser.bitbrowser.timeout
                },
                'adspower': {
                    'api_url': self.browser.adspower.api_url,
                    'timeout': self.browser.adspower.timeout
                }
            },
            'logging': {
                'log_dir': self.logging.log_dir,
                'max_log_files': self.logging.max_log_files,
                'max_log_size_mb': self.logging.max_log_size_mb,
                'log_format': self.logging.log_format,
                'date_format': self.logging.date_format
            },
            'data': {
                'profiles_file': self.data.profiles_file,
                'cache_dir': self.data.cache_dir,
                'backup_dir': self.data.backup_dir,
                'auto_backup': self.data.auto_backup,
                'backup_interval_hours': self.data.backup_interval_hours
            }
        }
    
    def save_to_file(self, config_file: str = "config.json"):
        """
        保存配置到文件
        
        Args:
            config_file: 配置文件路径
        """
        # 确保目录存在
        Path(config_file).parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
    
    def validate(self) -> List[str]:
        """
        验证配置的有效性
        
        Returns:
            List[str]: 验证错误列表，空列表表示验证通过
        """
        errors = []
        
        # 验证窗口尺寸
        if self.settings.window_width < self.settings.window_min_width:
            errors.append(f"窗口宽度不能小于最小宽度: {self.settings.window_min_width}")
        
        if self.settings.window_height < self.settings.window_min_height:
            errors.append(f"窗口高度不能小于最小高度: {self.settings.window_min_height}")
        
        # 验证并发任务数
        if self.settings.max_concurrent_tasks < 1:
            errors.append("最大并发任务数不能小于1")
        
        # 验证重试次数
        if self.settings.retry_count < 0:
            errors.append("重试次数不能小于0")
        
        # 验证浏览器类型
        if self.browser.default_type not in ['bitbrowser', 'adspower']:
            errors.append(f"不支持的默认浏览器类型: {self.browser.default_type}")
        
        # 验证API URL
        if not self.browser.bitbrowser.api_url.startswith('http'):
            errors.append("比特浏览器API URL格式错误")
        
        if not self.browser.adspower.api_url.startswith('http'):
            errors.append("AdsPower API URL格式错误")
        
        return errors
