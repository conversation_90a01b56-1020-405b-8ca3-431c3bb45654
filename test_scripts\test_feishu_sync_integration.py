#!/usr/bin/env python3
"""
飞书同步集成测试脚本

测试完整的飞书同步功能，包括：
1. 飞书客户端精确查询功能
2. 视频数据模型的账号字段
3. 任务管理器的飞书同步集成
4. 端到端的数据同步流程
"""

import sys
import os
import asyncio
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_video_data_with_account():
    """测试视频数据模型的账号字段"""
    print("\n测试视频数据模型的账号字段...")
    
    try:
        from core.scrapers.youtube_scraper import YouTubeVideoData
        
        # 创建包含账号信息的视频数据
        video_data = YouTubeVideoData(
            title="Test Video Title",
            views=12345,
            comments=67,
            likes=890,
            like_ratio=0.95,
            account="Test Channel"
        )
        
        # 验证账号字段
        assert video_data.account == "Test Channel", "账号字段设置失败"
        
        # 验证to_dict方法包含账号信息
        data_dict = video_data.to_dict()
        assert 'account' in data_dict, "to_dict方法缺少账号字段"
        assert data_dict['account'] == "Test Channel", "to_dict方法账号字段值错误"
        
        print("✓ 视频数据模型账号字段测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 视频数据模型账号字段测试失败: {str(e)}")
        return False

def test_feishu_client_precise_search():
    """测试飞书客户端精确查询功能"""
    print("\n测试飞书客户端精确查询功能...")
    
    try:
        from core.apis.feishu_client import FeishuClient
        
        # 创建测试配置
        config = {
            'app_id': 'test_app_id',
            'app_secret': 'test_app_secret',
            'app_token': 'test_app_token',
            'table_id': 'test_table_id',
            'field_mapping': {
                'title': '英文标题',
                'views': '播放量',
                'comments': '评论数',
                'likes': '赞数',
                'like_ratio': '赞比例',
                'account': '发布账号'
            }
        }
        
        client = FeishuClient(config)
        
        # 验证新方法存在
        assert hasattr(client, 'search_records_by_title_and_account'), "缺少精确查询方法"
        
        print("✓ 飞书客户端精确查询方法存在")
        
        # 模拟API调用测试
        with patch('requests.post') as mock_post:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                'code': 0,
                'data': {
                    'items': [
                        {
                            'record_id': 'test_record_id',
                            'fields': {
                                '英文标题': 'Test Title',
                                '发布账号': 'Test Account'
                            }
                        }
                    ]
                }
            }
            mock_post.return_value = mock_response
            
            # 模拟获取访问令牌
            with patch.object(client, 'get_access_token', return_value='test_token'):
                # 测试精确查询
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    records = loop.run_until_complete(
                        client.search_records_by_title_and_account("Test Title", "Test Account")
                    )
                    
                    assert len(records) == 1, "查询结果数量错误"
                    assert records[0]['record_id'] == 'test_record_id', "记录ID错误"
                    
                    # 验证请求体格式
                    call_args = mock_post.call_args
                    request_body = call_args[1]['json']
                    
                    assert 'field_names' in request_body, "请求体缺少field_names"
                    assert 'filter' in request_body, "请求体缺少filter"
                    assert request_body['filter']['conjunction'] == 'and', "过滤条件连接符错误"
                    assert len(request_body['filter']['conditions']) == 2, "过滤条件数量错误"
                    
                    print("✓ 飞书客户端精确查询功能测试通过")
                    return True
                    
                finally:
                    loop.close()
        
    except Exception as e:
        print(f"✗ 飞书客户端精确查询功能测试失败: {str(e)}")
        return False

def test_task_manager_feishu_integration():
    """测试任务管理器飞书集成"""
    print("\n测试任务管理器飞书集成...")
    
    try:
        from core.tasks.youtube_task_manager import YouTubeTaskManager
        
        # 创建包含飞书配置的测试配置
        config = {
            'max_concurrent_tasks': 1,
            'task_timeout': 60,
            'retry_count': 1,
            'retry_delay': 1,
            'feishu': {
                'app_id': 'test_app_id',
                'app_secret': 'test_app_secret',
                'app_token': 'test_app_token',
                'table_id_content': 'test_table_id',
                'field_mapping': {
                    'title': '英文标题',
                    'views': '播放量',
                    'comments': '评论数',
                    'likes': '赞数',
                    'like_ratio': '赞比例',
                    'account': '发布账号'
                }
            }
        }
        
        task_manager = YouTubeTaskManager(config)
        
        # 验证飞书客户端已初始化
        assert task_manager.feishu_client is not None, "飞书客户端未初始化"
        
        # 验证飞书同步方法存在
        assert hasattr(task_manager, '_sync_to_feishu'), "缺少飞书同步方法"
        
        print("✓ 任务管理器飞书集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 任务管理器飞书集成测试失败: {str(e)}")
        return False

def test_sync_video_data_with_account():
    """测试使用账号信息的视频数据同步"""
    print("\n测试使用账号信息的视频数据同步...")
    
    try:
        from core.apis.feishu_client import FeishuClient
        from core.scrapers.youtube_scraper import YouTubeVideoData
        
        # 创建测试配置
        config = {
            'app_id': 'test_app_id',
            'app_secret': 'test_app_secret',
            'app_token': 'test_app_token',
            'table_id': 'test_table_id',
            'field_mapping': {
                'title': '英文标题',
                'views': '播放量',
                'comments': '评论数',
                'likes': '赞数',
                'like_ratio': '赞比例',
                'account': '发布账号'
            }
        }
        
        client = FeishuClient(config)
        
        # 创建包含账号信息的视频数据
        video_data = YouTubeVideoData(
            title="Test Video",
            views=1000,
            comments=50,
            likes=100,
            like_ratio=0.9,
            account="Test Channel"
        )
        
        # 模拟同步过程
        with patch.object(client, 'search_records_by_title_and_account') as mock_search, \
             patch.object(client, 'update_record') as mock_update:
            
            # 模拟找到现有记录
            mock_search.return_value = [{'record_id': 'test_record_id'}]
            mock_update.return_value = True
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                success, message = loop.run_until_complete(
                    client.sync_video_data(video_data)
                )
                
                assert success, "同步应该成功"
                assert "Test Channel" in message, "同步消息应包含账号信息"
                
                # 验证调用了精确查询
                mock_search.assert_called_once_with("Test Video", "Test Channel")
                
                print("✓ 使用账号信息的视频数据同步测试通过")
                return True
                
            finally:
                loop.close()
        
    except Exception as e:
        print(f"✗ 使用账号信息的视频数据同步测试失败: {str(e)}")
        return False

def test_imports():
    """测试模块导入"""
    print("\n测试模块导入...")
    
    try:
        # 测试核心模块导入
        from core.apis.feishu_client import FeishuClient
        print("✓ 飞书客户端模块导入成功")
        
        from core.scrapers.youtube_scraper import YouTubeVideoData
        print("✓ YouTube数据模块导入成功")
        
        from core.tasks.youtube_task_manager import YouTubeTaskManager
        print("✓ 任务管理器模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模块导入测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始飞书同步集成测试...")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("视频数据模型账号字段", test_video_data_with_account),
        ("飞书客户端精确查询", test_feishu_client_precise_search),
        ("任务管理器飞书集成", test_task_manager_feishu_integration),
        ("视频数据同步（含账号）", test_sync_video_data_with_account),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {str(e)}")
    
    print(f"\n{'='*60}")
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！飞书同步集成功能正常")
        return True
    else:
        print("❌ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
