# 飞书多维表格同步功能使用说明

## 功能概述

本系统已完整集成飞书多维表格API，在YouTube数据抓取完成后，会自动将数据同步到飞书多维表格中。同步功能支持：

- **精确查询**：使用英文标题和发布账号两个字段进行精确匹配
- **智能更新**：如果记录已存在则更新，不存在则创建新记录
- **批量同步**：支持批量处理多个视频数据
- **进度显示**：实时显示同步进度和状态
- **错误处理**：完善的错误处理和重试机制

## 配置要求

### 1. 飞书应用配置

在 `config.json` 中配置飞书API信息：

```json
{
  "feishu": {
    "app_id": "你的应用ID",
    "app_secret": "你的应用密钥",
    "app_token": "你的多维表格应用令牌",
    "table_id_content": "内容表格ID",
    "table_id_account": "账号表格ID",
    "api_base_url": "https://open.feishu.cn/open-apis",
    "field_mapping": {
      "title": "英文标题",
      "views": "播放量",
      "comments": "评论数",
      "likes": "赞数",
      "like_ratio": "赞比例",
      "account": "发布账号"
    }
  }
}
```

### 2. 多维表格字段设置

确保你的飞书多维表格包含以下字段：

| 字段名称 | 字段类型 | 说明 |
|---------|---------|------|
| 英文标题 | 文本 | 视频标题 |
| 播放量 | 数字 | 视频播放量 |
| 评论数 | 数字 | 视频评论数 |
| 赞数 | 数字 | 视频点赞数 |
| 赞比例 | 文本 | 点赞比例（百分比格式） |
| 发布账号 | 文本 | YouTube频道名称 |

## 使用方式

### 自动同步

当你通过系统执行YouTube数据抓取任务时，系统会自动：

1. **抓取数据**：从YouTube Studio获取视频数据
2. **精确查询**：使用标题和账号在飞书表格中查找现有记录
3. **智能更新**：
   - 如果找到匹配记录，更新播放量、评论数、赞数等数据
   - 如果没有找到，创建新的记录
4. **进度反馈**：在界面上显示同步进度和结果

### 查询逻辑

系统使用以下查询条件进行精确匹配：

```json
{
  "field_names": ["英文标题", "发布账号"],
  "filter": {
    "conjunction": "and",
    "conditions": [
      {
        "field_name": "英文标题",
        "operator": "contains",
        "value": ["视频标题"]
      },
      {
        "field_name": "发布账号",
        "operator": "is",
        "value": ["频道名称"]
      }
    ]
  }
}
```

## 功能特点

### 1. 精确匹配
- 使用标题和账号双重条件，避免误匹配
- 支持标题的模糊匹配（contains）和账号的精确匹配（is）

### 2. 智能同步
- 自动检测记录是否存在
- 存在则更新，不存在则创建
- 保持数据的一致性和完整性

### 3. 批量处理
- 支持一次性同步多个视频数据
- 提供详细的同步统计信息
- 记录失败项目便于排查

### 4. 错误处理
- 网络异常自动重试
- 详细的错误日志记录
- 不影响主要的数据抓取流程

## 监控和日志

### 界面显示
- 任务执行页面会显示飞书同步的进度
- 日志中会用绿色标识飞书同步相关信息
- 任务完成后会显示同步结果统计

### 日志信息
系统会记录以下同步信息：
- 同步开始和完成时间
- 成功/失败的记录数量
- 创建/更新的记录统计
- 详细的错误信息（如有）

## 故障排除

### 常见问题

1. **配置不完整**
   - 检查 `config.json` 中的飞书配置是否完整
   - 确认应用ID、密钥、表格ID等信息正确

2. **权限问题**
   - 确保飞书应用有访问多维表格的权限
   - 检查表格的共享设置

3. **字段映射错误**
   - 确认多维表格中的字段名称与配置中的映射一致
   - 注意字段名称的大小写和空格

4. **网络连接问题**
   - 检查网络连接是否正常
   - 确认可以访问飞书API服务

### 调试方法

1. **查看日志**：检查应用日志中的飞书同步相关信息
2. **测试连接**：使用飞书配置对话框的"测试连接"功能
3. **单独测试**：运行 `test_scripts/test_feishu_sync_integration.py` 进行功能测试

## API参考

### 飞书多维表格API文档
- [搜索记录API](https://open.feishu.cn/document/docs/bitable-v1/app-table-record/search)
- [更新记录API](https://open.feishu.cn/document/docs/bitable-v1/app-table-record/update)
- [创建记录API](https://open.feishu.cn/document/docs/bitable-v1/app-table-record/create)

### 核心类和方法

- `FeishuClient.search_records_by_title_and_account()`: 精确查询记录
- `FeishuClient.sync_video_data()`: 同步单个视频数据
- `FeishuClient.batch_sync_video_data()`: 批量同步视频数据
- `YouTubeTaskManager._sync_to_feishu()`: 任务管理器中的同步方法

## 更新日志

### v1.0.0 (2025-09-18)
- ✅ 实现飞书多维表格API集成
- ✅ 支持使用标题和账号的精确查询
- ✅ 集成到YouTube数据抓取流程
- ✅ 添加进度显示和错误处理
- ✅ 完善的测试覆盖
