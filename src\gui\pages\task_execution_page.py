"""
任务执行和监控页面

提供YouTube数据抓取任务的执行和监控功能，包括：
1. 任务配置和启动
2. 实时状态显示
3. 进度监控
4. 日志查看
5. 结果统计
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import asyncio
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

try:
    from ..components.styles import Colors, Fonts, Spacing, create_card_frame
    from ..dialogs.feishu_config_dialog import FeishuConfigDialog
    from ...core.tasks.youtube_task_manager import YouTubeTaskManager
    from ...core.apis.feishu_client import FeishuClient
    from ...models.profile import ProfileManager
    from ...utils.logger import get_logger
except ImportError:
    # 处理测试环境的导入
    from gui.components.styles import Colors, Fonts, Spacing, create_card_frame
    from gui.dialogs.feishu_config_dialog import FeishuConfigDialog
    from core.tasks.youtube_task_manager import YouTube<PERSON>askManager
    from core.apis.feishu_client import FeishuClient
    from models.profile import ProfileManager
    from utils.logger import get_logger


class TaskExecutionPage:
    """任务执行和监控页面"""
    
    def __init__(self, parent, config: Dict[str, Any]):
        """
        初始化页面
        
        Args:
            parent: 父控件
            config: 应用配置
        """
        self.parent = parent
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        
        # 任务管理器
        self.task_manager: Optional[YouTubeTaskManager] = None
        self.profile_manager = ProfileManager(config.get('profiles_file', 'data/profiles.json'))
        
        # 界面状态
        self.is_running = False
        self.selected_profiles = []
        
        # 创建界面
        self._create_widgets()
        
        # 加载数据
        self._load_profiles()
        
        self.logger.info("任务执行页面初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent, style="Page.TFrame")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左右分栏
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 左侧控制面板
        self._create_control_panel(paned_window)
        
        # 右侧监控面板
        self._create_monitor_panel(paned_window)
    
    def _create_control_panel(self, parent):
        """创建控制面板"""
        # 控制面板框架
        control_frame = ttk.Frame(parent, style="Page.TFrame")
        parent.add(control_frame, weight=1)
        
        # 标题
        title_label = ttk.Label(
            control_frame,
            text="任务控制",
            style="Heading.TLabel"
        )
        title_label.pack(anchor=tk.W, pady=(0, 20))
        
        # Profile选择区域
        self._create_profile_selection(control_frame)
        
        # 任务配置区域
        self._create_task_config(control_frame)
        
        # 飞书配置区域
        self._create_feishu_config(control_frame)
        
        # 控制按钮区域
        self._create_control_buttons(control_frame)
    
    def _create_profile_selection(self, parent):
        """创建Profile选择区域"""
        # Profile选择卡片
        profile_card = create_card_frame(parent)
        profile_card.pack(fill=tk.X, pady=(0, 15))
        
        # 标题
        ttk.Label(
            profile_card,
            text="选择Profile",
            style="Subheading.TLabel"
        ).pack(anchor=tk.W, padx=15, pady=(15, 10))
        
        # Profile列表框架
        list_frame = ttk.Frame(profile_card, style="Card.TFrame")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        # Profile列表
        self.profile_listbox = tk.Listbox(
            list_frame,
            selectmode=tk.EXTENDED,
            height=6,
            bg=Colors.SURFACE,
            fg=Colors.TEXT_PRIMARY,
            selectbackground=Colors.PRIMARY,
            selectforeground=Colors.TEXT_ON_PRIMARY,
            borderwidth=1,
            relief="solid"
        )
        self.profile_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 滚动条
        profile_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL)
        profile_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.profile_listbox.config(yscrollcommand=profile_scrollbar.set)
        profile_scrollbar.config(command=self.profile_listbox.yview)
        
        # 选择按钮
        button_frame = ttk.Frame(profile_card, style="Card.TFrame")
        button_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        ttk.Button(
            button_frame,
            text="全选",
            style="Secondary.TButton",
            command=self._select_all_profiles
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            button_frame,
            text="清空",
            style="Secondary.TButton",
            command=self._clear_profile_selection
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            button_frame,
            text="仅活跃",
            style="Secondary.TButton",
            command=self._select_active_profiles
        ).pack(side=tk.LEFT, padx=5)
    
    def _create_task_config(self, parent):
        """创建任务配置区域"""
        # 任务配置卡片
        config_card = create_card_frame(parent)
        config_card.pack(fill=tk.X, pady=(0, 15))
        
        # 标题
        ttk.Label(
            config_card,
            text="任务配置",
            style="Subheading.TLabel"
        ).pack(anchor=tk.W, padx=15, pady=(15, 10))
        
        # 配置框架
        config_frame = ttk.Frame(config_card, style="Card.TFrame")
        config_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        # 并发数配置
        ttk.Label(config_frame, text="最大并发数:", style="Body.TLabel").grid(
            row=0, column=0, sticky=tk.W, pady=(0, 5)
        )
        self.concurrent_var = tk.IntVar(value=self.config.get('max_concurrent_tasks', 3))
        concurrent_spinbox = ttk.Spinbox(
            config_frame,
            from_=1,
            to=10,
            textvariable=self.concurrent_var,
            width=10
        )
        concurrent_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=(0, 5))
        
        # 任务超时配置
        ttk.Label(config_frame, text="任务超时 (分钟):", style="Body.TLabel").grid(
            row=1, column=0, sticky=tk.W, pady=5
        )
        self.timeout_var = tk.IntVar(value=self.config.get('task_timeout', 30))
        timeout_spinbox = ttk.Spinbox(
            config_frame,
            from_=5,
            to=120,
            textvariable=self.timeout_var,
            width=10
        )
        timeout_spinbox.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 重试次数配置
        ttk.Label(config_frame, text="重试次数:", style="Body.TLabel").grid(
            row=2, column=0, sticky=tk.W, pady=5
        )
        self.retry_var = tk.IntVar(value=self.config.get('retry_count', 3))
        retry_spinbox = ttk.Spinbox(
            config_frame,
            from_=0,
            to=10,
            textvariable=self.retry_var,
            width=10
        )
        retry_spinbox.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 同步到飞书选项
        self.sync_feishu_var = tk.BooleanVar(value=True)
        sync_checkbox = ttk.Checkbutton(
            config_frame,
            text="同步到飞书",
            variable=self.sync_feishu_var,
            style="Modern.TCheckbutton"
        )
        sync_checkbox.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))
    
    def _create_feishu_config(self, parent):
        """创建飞书配置区域"""
        # 飞书配置卡片
        feishu_card = create_card_frame(parent)
        feishu_card.pack(fill=tk.X, pady=(0, 15))
        
        # 标题
        ttk.Label(
            feishu_card,
            text="飞书配置",
            style="Subheading.TLabel"
        ).pack(anchor=tk.W, padx=15, pady=(15, 10))
        
        # 配置状态
        status_frame = ttk.Frame(feishu_card, style="Card.TFrame")
        status_frame.pack(fill=tk.X, padx=15, pady=(0, 10))
        
        self.feishu_status_label = ttk.Label(
            status_frame,
            text="未配置",
            style="Body.TLabel",
            foreground=Colors.WARNING
        )
        self.feishu_status_label.pack(side=tk.LEFT)
        
        # 配置按钮
        ttk.Button(
            status_frame,
            text="配置",
            style="Secondary.TButton",
            command=self._configure_feishu
        ).pack(side=tk.RIGHT)
        
        # 测试按钮
        self.test_feishu_button = ttk.Button(
            status_frame,
            text="测试",
            style="Secondary.TButton",
            command=self._test_feishu_connection,
            state='disabled'
        )
        self.test_feishu_button.pack(side=tk.RIGHT, padx=(0, 5))
    
    def _create_control_buttons(self, parent):
        """创建控制按钮区域"""
        # 按钮卡片
        button_card = create_card_frame(parent)
        button_card.pack(fill=tk.X)
        
        # 按钮框架
        button_frame = ttk.Frame(button_card, style="Card.TFrame")
        button_frame.pack(fill=tk.X, padx=15, pady=15)
        
        # 开始按钮
        self.start_button = ttk.Button(
            button_frame,
            text="开始抓取",
            style="Primary.TButton",
            command=self._start_tasks
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 停止按钮
        self.stop_button = ttk.Button(
            button_frame,
            text="停止抓取",
            style="Danger.TButton",
            command=self._stop_tasks,
            state='disabled'
        )
        self.stop_button.pack(side=tk.LEFT)
        
        # 状态标签
        self.status_label = ttk.Label(
            button_frame,
            text="就绪",
            style="Body.TLabel"
        )
        self.status_label.pack(side=tk.RIGHT)
    
    def _create_monitor_panel(self, parent):
        """创建监控面板"""
        # 监控面板框架
        monitor_frame = ttk.Frame(parent, style="Page.TFrame")
        parent.add(monitor_frame, weight=2)
        
        # 标题
        title_label = ttk.Label(
            monitor_frame,
            text="任务监控",
            style="Heading.TLabel"
        )
        title_label.pack(anchor=tk.W, pady=(0, 20))
        
        # 创建选项卡
        notebook = ttk.Notebook(monitor_frame, style="Modern.TNotebook")
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 任务状态页面
        self._create_task_status_tab(notebook)
        
        # 执行日志页面
        self._create_log_tab(notebook)
        
        # 结果统计页面
        self._create_results_tab(notebook)
    
    def _create_task_status_tab(self, notebook):
        """创建任务状态页面"""
        # 状态页面框架
        status_frame = ttk.Frame(notebook, style="Page.TFrame")
        notebook.add(status_frame, text="任务状态")
        
        # 总体状态
        summary_card = create_card_frame(status_frame)
        summary_card.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(
            summary_card,
            text="总体状态",
            style="Subheading.TLabel"
        ).pack(anchor=tk.W, padx=15, pady=(15, 10))
        
        # 状态统计框架
        stats_frame = ttk.Frame(summary_card, style="Card.TFrame")
        stats_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        # 统计标签
        self.total_tasks_label = ttk.Label(stats_frame, text="总任务: 0", style="Body.TLabel")
        self.total_tasks_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        
        self.running_tasks_label = ttk.Label(stats_frame, text="运行中: 0", style="Body.TLabel")
        self.running_tasks_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        self.completed_tasks_label = ttk.Label(stats_frame, text="已完成: 0", style="Body.TLabel")
        self.completed_tasks_label.grid(row=0, column=2, sticky=tk.W, padx=(0, 20))
        
        self.failed_tasks_label = ttk.Label(stats_frame, text="失败: 0", style="Body.TLabel")
        self.failed_tasks_label.grid(row=0, column=3, sticky=tk.W)
        
        # 任务详情
        details_card = create_card_frame(status_frame)
        details_card.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(
            details_card,
            text="任务详情",
            style="Subheading.TLabel"
        ).pack(anchor=tk.W, padx=15, pady=(15, 10))
        
        # 任务列表
        list_frame = ttk.Frame(details_card, style="Card.TFrame")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        # 创建Treeview
        columns = ('profile', 'status', 'progress', 'message', 'duration')
        self.task_tree = ttk.Treeview(
            list_frame,
            columns=columns,
            show='headings',
            style="Modern.Treeview"
        )
        
        # 设置列标题
        self.task_tree.heading('profile', text='Profile')
        self.task_tree.heading('status', text='状态')
        self.task_tree.heading('progress', text='进度')
        self.task_tree.heading('message', text='消息')
        self.task_tree.heading('duration', text='耗时')
        
        # 设置列宽
        self.task_tree.column('profile', width=150)
        self.task_tree.column('status', width=80)
        self.task_tree.column('progress', width=80)
        self.task_tree.column('message', width=200)
        self.task_tree.column('duration', width=80)
        
        self.task_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 滚动条
        task_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL)
        task_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.task_tree.config(yscrollcommand=task_scrollbar.set)
        task_scrollbar.config(command=self.task_tree.yview)
    
    def _create_log_tab(self, notebook):
        """创建日志页面"""
        # 日志页面框架
        log_frame = ttk.Frame(notebook, style="Page.TFrame")
        notebook.add(log_frame, text="执行日志")
        
        # 日志文本框
        self.log_text = tk.Text(
            log_frame,
            bg=Colors.SURFACE,
            fg=Colors.TEXT_PRIMARY,
            insertbackground=Colors.TEXT_PRIMARY,
            selectbackground=Colors.PRIMARY,
            selectforeground=Colors.TEXT_ON_PRIMARY,
            borderwidth=1,
            relief="solid",
            wrap=tk.WORD
        )
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 配置日志文本标签样式
        self.log_text.tag_configure("feishu_sync", foreground="#4CAF50")  # 绿色表示飞书同步

        # 日志滚动条
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 15), pady=15)
        
        self.log_text.config(yscrollcommand=log_scrollbar.set)
        log_scrollbar.config(command=self.log_text.yview)
    
    def _create_results_tab(self, notebook):
        """创建结果统计页面"""
        # 结果页面框架
        results_frame = ttk.Frame(notebook, style="Page.TFrame")
        notebook.add(results_frame, text="结果统计")
        
        # 统计卡片
        stats_card = create_card_frame(results_frame)
        stats_card.pack(fill=tk.X, padx=15, pady=15)
        
        ttk.Label(
            stats_card,
            text="抓取统计",
            style="Subheading.TLabel"
        ).pack(anchor=tk.W, padx=15, pady=(15, 10))
        
        # 统计信息框架
        stats_info_frame = ttk.Frame(stats_card, style="Card.TFrame")
        stats_info_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        # 统计标签
        self.scraped_videos_label = ttk.Label(stats_info_frame, text="抓取视频: 0", style="Body.TLabel")
        self.scraped_videos_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 20), pady=5)
        
        self.synced_videos_label = ttk.Label(stats_info_frame, text="同步成功: 0", style="Body.TLabel")
        self.synced_videos_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 20), pady=5)
        
        self.sync_failed_label = ttk.Label(stats_info_frame, text="同步失败: 0", style="Body.TLabel")
        self.sync_failed_label.grid(row=0, column=2, sticky=tk.W, pady=5)
        
        # 结果详情（预留）
        results_card = create_card_frame(results_frame)
        results_card.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        ttk.Label(
            results_card,
            text="详细结果",
            style="Subheading.TLabel"
        ).pack(anchor=tk.W, padx=15, pady=(15, 10))
        
        # 结果文本框
        self.results_text = tk.Text(
            results_card,
            bg=Colors.SURFACE,
            fg=Colors.TEXT_PRIMARY,
            insertbackground=Colors.TEXT_PRIMARY,
            borderwidth=1,
            relief="solid",
            wrap=tk.WORD,
            state='disabled'
        )
        self.results_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

    def _load_profiles(self):
        """加载Profile列表"""
        try:
            profiles = self.profile_manager.get_all_profiles()

            # 清空列表
            self.profile_listbox.delete(0, tk.END)

            # 添加Profile
            for profile in profiles:
                status = "✓" if profile.is_active else "✗"
                display_text = f"{status} {profile.name} ({profile.browser_type})"
                self.profile_listbox.insert(tk.END, display_text)

            self.logger.info(f"加载了 {len(profiles)} 个Profile")

        except Exception as e:
            self.logger.error(f"加载Profile失败: {str(e)}")
            messagebox.showerror("错误", f"加载Profile失败: {str(e)}")

    def _select_all_profiles(self):
        """全选Profile"""
        self.profile_listbox.select_set(0, tk.END)

    def _clear_profile_selection(self):
        """清空Profile选择"""
        self.profile_listbox.selection_clear(0, tk.END)

    def _select_active_profiles(self):
        """选择活跃Profile"""
        self.profile_listbox.selection_clear(0, tk.END)

        profiles = self.profile_manager.get_all_profiles()
        for i, profile in enumerate(profiles):
            if profile.is_active:
                self.profile_listbox.select_set(i)

    def _get_selected_profiles(self):
        """获取选中的Profile"""
        selected_indices = self.profile_listbox.curselection()
        profiles = self.profile_manager.get_all_profiles()

        selected_profiles = []
        for index in selected_indices:
            if index < len(profiles):
                selected_profiles.append(profiles[index])

        return selected_profiles

    def _configure_feishu(self):
        """配置飞书"""
        try:
            # 获取当前配置
            current_config = self.config.get('feishu', {})

            # 显示配置对话框
            dialog = FeishuConfigDialog(self.parent, current_config)
            result = dialog.show()

            if result:
                # 保存配置
                self.config['feishu'] = result

                # 更新状态
                self._update_feishu_status()

                self.logger.info("飞书配置已更新")
                messagebox.showinfo("成功", "飞书配置已保存")

        except Exception as e:
            self.logger.error(f"配置飞书失败: {str(e)}")
            messagebox.showerror("错误", f"配置飞书失败: {str(e)}")

    def _update_feishu_status(self):
        """更新飞书配置状态"""
        feishu_config = self.config.get('feishu', {})

        if (feishu_config.get('app_id') and feishu_config.get('app_secret') and
            feishu_config.get('app_token') and feishu_config.get('table_id')):
            self.feishu_status_label.config(text="已配置", foreground=Colors.SUCCESS)
            self.test_feishu_button.config(state='normal')
        else:
            self.feishu_status_label.config(text="未配置", foreground=Colors.WARNING)
            self.test_feishu_button.config(state='disabled')

    def _test_feishu_connection(self):
        """测试飞书连接"""
        try:
            feishu_config = self.config.get('feishu', {})
            if not feishu_config:
                messagebox.showerror("错误", "请先配置飞书")
                return

            # 禁用按钮
            self.test_feishu_button.config(state='disabled', text='测试中...')

            def test_thread():
                try:
                    client = FeishuClient(feishu_config)
                    success, message = client.test_connection()

                    # 在主线程中显示结果
                    self.parent.after(0, lambda: self._show_feishu_test_result(success, message))

                except Exception as e:
                    error_msg = f"测试连接异常: {str(e)}"
                    self.parent.after(0, lambda: self._show_feishu_test_result(False, error_msg))

            # 在后台线程中执行测试
            threading.Thread(target=test_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"测试飞书连接失败: {str(e)}")
            messagebox.showerror("错误", f"测试飞书连接失败: {str(e)}")

    def _show_feishu_test_result(self, success: bool, message: str):
        """显示飞书测试结果"""
        # 恢复按钮状态
        self.test_feishu_button.config(state='normal', text='测试')

        if success:
            messagebox.showinfo("连接测试", message)
        else:
            messagebox.showerror("连接测试", message)

    def _start_tasks(self):
        """开始任务"""
        try:
            # 获取选中的Profile
            selected_profiles = self._get_selected_profiles()
            if not selected_profiles:
                messagebox.showwarning("警告", "请选择要执行的Profile")
                return

            # 检查飞书配置
            if self.sync_feishu_var.get():
                feishu_config = self.config.get('feishu', {})
                if not (feishu_config.get('app_id') and feishu_config.get('app_secret')):
                    messagebox.showerror("错误", "启用飞书同步时，请先配置飞书")
                    return

            # 更新界面状态
            self.is_running = True
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')
            self.status_label.config(text="正在启动...")

            # 清空监控界面
            self._clear_monitor_data()

            # 创建任务配置
            task_config = {
                'max_concurrent_tasks': self.concurrent_var.get(),
                'task_timeout': self.timeout_var.get() * 60,  # 转换为秒
                'retry_count': self.retry_var.get(),
                'retry_delay': 60,
                'browser': self.config.get('browser', {}),
                'scraping': self.config.get('scraping', {}),
                'feishu': self.config.get('feishu', {}) if self.sync_feishu_var.get() else None
            }

            # 创建任务管理器
            self.task_manager = YouTubeTaskManager(task_config)
            self.task_manager.set_progress_callback(self._on_task_progress)
            self.task_manager.set_completion_callback(self._on_tasks_completed)

            # 在后台线程中启动任务
            def start_thread():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        success = loop.run_until_complete(
                            self.task_manager.start_scraping_tasks(selected_profiles)
                        )

                        if not success:
                            self.parent.after(0, lambda: self._on_start_failed("任务启动失败"))
                    finally:
                        loop.close()

                except Exception as e:
                    error_msg = f"启动任务异常: {str(e)}"
                    self.parent.after(0, lambda: self._on_start_failed(error_msg))

            threading.Thread(target=start_thread, daemon=True).start()

            self.logger.info(f"开始执行 {len(selected_profiles)} 个Profile的抓取任务")

        except Exception as e:
            self.logger.error(f"启动任务失败: {str(e)}")
            messagebox.showerror("错误", f"启动任务失败: {str(e)}")
            self._reset_ui_state()

    def _stop_tasks(self):
        """停止任务"""
        try:
            if self.task_manager:
                self.task_manager.stop_scraping_tasks()
                self.status_label.config(text="正在停止...")
                self.logger.info("请求停止任务")

        except Exception as e:
            self.logger.error(f"停止任务失败: {str(e)}")
            messagebox.showerror("错误", f"停止任务失败: {str(e)}")

    def _on_start_failed(self, error_msg: str):
        """任务启动失败处理"""
        self.logger.error(error_msg)
        messagebox.showerror("错误", error_msg)
        self._reset_ui_state()

    def _on_task_progress(self, task_id: str, task, message: str):
        """任务进度回调"""
        try:
            # 更新任务状态显示
            self._update_task_display()

            # 添加日志
            timestamp = datetime.now().strftime("%H:%M:%S")

            # 处理飞书同步进度信息
            if "飞书同步" in message:
                profile_name = task.profile.name if task and hasattr(task, 'profile') else "未知Profile"
                log_message = f"[{timestamp}] {profile_name}: {message}\n"
                # 使用不同颜色标识飞书同步
                self.log_text.insert(tk.END, log_message, "feishu_sync")
            else:
                profile_name = task.profile.name if task and hasattr(task, 'profile') else "未知Profile"
                log_message = f"[{timestamp}] {profile_name}: {message}\n"
                self.log_text.insert(tk.END, log_message)

            self.log_text.see(tk.END)

        except Exception as e:
            self.logger.error(f"处理任务进度失败: {str(e)}")

    def _on_tasks_completed(self, status: Dict[str, Any]):
        """任务完成回调"""
        try:
            self.logger.info(f"所有任务完成: {status}")

            # 更新界面状态
            self._reset_ui_state()
            self.status_label.config(text="任务完成")

            # 更新统计信息
            self._update_results_display()

            # 显示完成消息
            total = status.get('total_tasks', 0)
            completed = status.get('completed_tasks', 0)
            failed = status.get('failed_tasks', 0)

            # 检查是否有飞书同步结果
            feishu_sync_info = ""
            if hasattr(self, 'feishu_sync_results') and self.feishu_sync_results:
                total_synced = sum(r.get('success_count', 0) for r in self.feishu_sync_results.values())
                total_failed = sum(r.get('failed_count', 0) for r in self.feishu_sync_results.values())
                feishu_sync_info = f"\n\n飞书同步结果:\n成功: {total_synced}\n失败: {total_failed}"

            message = f"任务执行完成！\n总计: {total}\n成功: {completed}\n失败: {failed}{feishu_sync_info}"
            messagebox.showinfo("任务完成", message)

        except Exception as e:
            self.logger.error(f"处理任务完成失败: {str(e)}")

    def _clear_monitor_data(self):
        """清空监控数据"""
        # 清空任务树
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)

        # 清空日志
        self.log_text.delete(1.0, tk.END)

        # 清空结果
        self.results_text.config(state='normal')
        self.results_text.delete(1.0, tk.END)
        self.results_text.config(state='disabled')

        # 重置统计
        self._update_task_stats(0, 0, 0, 0)
        self._update_results_stats(0, 0, 0)

    def _update_task_display(self):
        """更新任务显示"""
        if not self.task_manager:
            return

        try:
            # 获取任务状态
            status = self.task_manager.get_task_status()
            details = self.task_manager.get_task_details()

            # 更新统计
            self._update_task_stats(
                status.get('total_tasks', 0),
                status.get('running_tasks', 0),
                status.get('completed_tasks', 0),
                status.get('failed_tasks', 0)
            )

            # 更新任务树
            # 清空现有项目
            for item in self.task_tree.get_children():
                self.task_tree.delete(item)

            # 添加任务详情
            for detail in details:
                progress = f"{detail['progress']:.1%}" if detail['progress'] else "0%"
                duration = detail['duration'] or ""

                self.task_tree.insert('', tk.END, values=(
                    detail['profile_name'],
                    detail['status'],
                    progress,
                    detail['message'] or "",
                    duration
                ))

        except Exception as e:
            self.logger.error(f"更新任务显示失败: {str(e)}")

    def _update_task_stats(self, total: int, running: int, completed: int, failed: int):
        """更新任务统计"""
        self.total_tasks_label.config(text=f"总任务: {total}")
        self.running_tasks_label.config(text=f"运行中: {running}")
        self.completed_tasks_label.config(text=f"已完成: {completed}")
        self.failed_tasks_label.config(text=f"失败: {failed}")

    def _update_results_display(self):
        """更新结果显示"""
        if not self.task_manager:
            return

        try:
            # 获取所有结果
            all_results = self.task_manager.get_all_results()

            total_videos = 0
            synced_videos = 0
            sync_failed = 0

            results_text = "抓取结果详情:\n\n"

            for task_id, videos in all_results.items():
                total_videos += len(videos)
                results_text += f"{task_id}: {len(videos)} 个视频\n"

            # 更新统计
            self._update_results_stats(total_videos, synced_videos, sync_failed)

            # 更新结果文本
            self.results_text.config(state='normal')
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(1.0, results_text)
            self.results_text.config(state='disabled')

        except Exception as e:
            self.logger.error(f"更新结果显示失败: {str(e)}")

    def _update_results_stats(self, scraped: int, synced: int, failed: int):
        """更新结果统计"""
        self.scraped_videos_label.config(text=f"抓取视频: {scraped}")
        self.synced_videos_label.config(text=f"同步成功: {synced}")
        self.sync_failed_label.config(text=f"同步失败: {failed}")

    def _reset_ui_state(self):
        """重置界面状态"""
        self.is_running = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.status_label.config(text="就绪")

    def refresh(self):
        """刷新页面"""
        self._load_profiles()
        self._update_feishu_status()

        if not self.is_running:
            self._clear_monitor_data()
