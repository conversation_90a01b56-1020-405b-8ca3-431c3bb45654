"""
飞书API客户端

负责与飞书多维表格进行数据同步，包括：
1. 获取访问令牌
2. 查询现有记录
3. 更新数据记录
4. 批量数据操作
5. 错误处理和重试
"""

import requests
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging

try:
    from ...utils.logger import get_logger, TaskLogger
    from ..scrapers.youtube_scraper import YouTubeVideoData
except ImportError:
    # 处理测试环境的导入
    from utils.logger import get_logger, TaskLogger
    from core.scrapers.youtube_scraper import YouTubeVideoData


class FeishuAPIError(Exception):
    """飞书API异常"""
    pass


class FeishuClient:
    """飞书API客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化飞书客户端
        
        Args:
            config: 飞书配置信息
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        
        # API配置
        self.app_id = config.get('app_id', '')
        self.app_secret = config.get('app_secret', '')
        self.app_token = config.get('app_token', '')
        self.table_id = config.get('table_id', '')
        
        # API端点
        self.base_url = "https://open.feishu.cn/open-apis"
        self.token_url = f"{self.base_url}/auth/v3/tenant_access_token/internal"
        self.records_url = f"{self.base_url}/bitable/v1/apps/{self.app_token}/tables/{self.table_id}/records"
        
        # 字段映射
        self.field_mapping = config.get('field_mapping', {
            'title': '英文标题',
            'views': '播放量',
            'account': '发布账号',
            'comments': '评论数',
            'likes': '赞数',
            'like_ratio': '赞比例'
        })
        
        # 请求配置
        self.timeout = config.get('timeout', 30)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1)
        
        # 缓存访问令牌
        self._access_token = None
        self._token_expires_at = None
        
        self.logger.info("飞书API客户端初始化完成")
    
    async def get_access_token(self) -> str:
        """
        获取访问令牌
        
        Returns:
            str: 访问令牌
        """
        # 检查缓存的令牌是否有效
        if (self._access_token and self._token_expires_at and 
            datetime.now() < self._token_expires_at - timedelta(minutes=5)):
            return self._access_token
        
        try:
            # 请求新的访问令牌
            payload = {
                "app_id": self.app_id,
                "app_secret": self.app_secret
            }
            
            response = requests.post(
                self.token_url,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                raise FeishuAPIError(f"获取访问令牌失败: HTTP {response.status_code}")
            
            data = response.json()
            
            if data.get('code') != 0:
                raise FeishuAPIError(f"获取访问令牌失败: {data.get('msg', '未知错误')}")
            
            # 缓存令牌
            self._access_token = data['tenant_access_token']
            expires_in = data.get('expire', 7200)  # 默认2小时
            self._token_expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            self.logger.info("成功获取飞书访问令牌")
            return self._access_token
            
        except requests.RequestException as e:
            raise FeishuAPIError(f"请求访问令牌失败: {str(e)}")
        except Exception as e:
            raise FeishuAPIError(f"获取访问令牌异常: {str(e)}")
    
    async def search_records_by_title_and_account(self, title: str, account: str) -> List[Dict[str, Any]]:
        """
        根据标题和发布账号搜索记录（精确查询）

        Args:
            title: 视频标题
            account: 发布账号

        Returns:
            List[Dict[str, Any]]: 匹配的记录列表
        """
        try:
            access_token = await self.get_access_token()

            # 构建搜索请求体
            search_body = {
                "field_names": [
                    self.field_mapping['title'],
                    self.field_mapping['account']
                ],
                "filter": {
                    "conjunction": "and",
                    "conditions": [
                        {
                            "field_name": self.field_mapping['title'],
                            "operator": "contains",
                            "value": [title]
                        },
                        {
                            "field_name": self.field_mapping['account'],
                            "operator": "is",
                            "value": [account]
                        }
                    ]
                },
                "automatic_fields": False
            }
            
            self.logger.debug(f"搜索请求体: {search_body}")

            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            # 使用POST方法进行搜索
            search_url = f"{self.base_url}/bitable/v1/apps/{self.app_token}/tables/{self.table_id}/records/search"

            response = requests.post(
                search_url,
                json=search_body,
                headers=headers,
                timeout=self.timeout
            )

            if response.status_code != 200:
                raise FeishuAPIError(f"搜索记录失败: HTTP {response.status_code}")

            data = response.json()

            if data.get('code') != 0:
                raise FeishuAPIError(f"搜索记录失败: {data}")

            records = data.get('data', {}).get('items', [])
            self.logger.debug(f"搜索到 {len(records)} 条记录: {title} - {account}")

            return records

        except FeishuAPIError:
            raise
        except Exception as e:
            raise FeishuAPIError(f"搜索记录异常: {str(e)}")

    async def search_records_by_title(self, title: str) -> List[Dict[str, Any]]:
        """
        根据标题搜索记录
        
        Args:
            title: 视频标题
            
        Returns:
            List[Dict[str, Any]]: 匹配的记录列表
        """
        try:
            access_token = await self.get_access_token()
            
            # 构建搜索参数
            params = {
                'filter': json.dumps({
                    'conditions': [{
                        'field_name': self.field_mapping['title'],
                        'operator': 'is',
                        'value': [title]
                    }]
                })
            }
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                self.records_url,
                params=params,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                raise FeishuAPIError(f"搜索记录失败: HTTP {response.status_code}")
            
            data = response.json()
            
            if data.get('code') != 0:
                raise FeishuAPIError(f"搜索记录失败: {data.get('msg', '未知错误')}")
            
            records = data.get('data', {}).get('items', [])
            self.logger.debug(f"搜索到 {len(records)} 条记录: {title}")
            
            return records
            
        except FeishuAPIError:
            raise
        except Exception as e:
            raise FeishuAPIError(f"搜索记录异常: {str(e)}")
    
    async def update_record(self, record_id: str, video_data: YouTubeVideoData) -> bool:
        """
        更新记录
        
        Args:
            record_id: 记录ID
            video_data: 视频数据
            
        Returns:
            bool: 是否更新成功
        """
        try:
            access_token = await self.get_access_token()
            
            # 构建更新数据
            fields = {
                self.field_mapping['views']: video_data.views,
                self.field_mapping['comments']: video_data.comments,
                self.field_mapping['likes']: video_data.likes,
                self.field_mapping['like_ratio']: video_data.like_ratio
            }
            
            payload = {
                'fields': fields
            }
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            url = f"{self.records_url}/{record_id}"
            
            response = requests.put(
                url,
                json=payload,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                raise FeishuAPIError(f"更新记录失败: HTTP {response.status_code}")
            
            data = response.json()
            
            if data.get('code') != 0:
                raise FeishuAPIError(f"更新记录失败: {data.get('msg', '未知错误')}")
            
            self.logger.debug(f"成功更新记录: {record_id}")
            return True
            
        except FeishuAPIError:
            raise
        except Exception as e:
            raise FeishuAPIError(f"更新记录异常: {str(e)}")
    
    async def create_record(self, video_data: YouTubeVideoData) -> str:
        """
        创建新记录
        
        Args:
            video_data: 视频数据
            
        Returns:
            str: 新记录的ID
        """
        try:
            access_token = await self.get_access_token()
            
            # 构建创建数据
            fields = {
                self.field_mapping['title']: video_data.title,
                self.field_mapping['views']: video_data.views,
                self.field_mapping['comments']: video_data.comments,
                self.field_mapping['likes']: video_data.likes,
                self.field_mapping['like_ratio']: f"{video_data.like_ratio:.2%}",
                self.field_mapping['account']: video_data.account
            }
            
            payload = {
                'fields': fields
            }
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                self.records_url,
                json=payload,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                raise FeishuAPIError(f"创建记录失败: HTTP {response.status_code}")
            
            data = response.json()
            
            if data.get('code') != 0:
                raise FeishuAPIError(f"创建记录失败: {data.get('msg', '未知错误')}")
            
            record_id = data.get('data', {}).get('record', {}).get('record_id', '')
            self.logger.debug(f"成功创建记录: {record_id}")
            
            return record_id
            
        except FeishuAPIError:
            raise
        except Exception as e:
            raise FeishuAPIError(f"创建记录异常: {str(e)}")
    
    async def sync_video_data(self, video_data: YouTubeVideoData) -> Tuple[bool, str]:
        """
        同步视频数据

        Args:
            video_data: 视频数据

        Returns:
            Tuple[bool, str]: (是否成功, 操作描述)
        """
        retry_count = 0

        while retry_count <= self.max_retries:
            try:
                # 使用标题和账号进行精确搜索
                existing_records = await self.search_records_by_title_and_account(
                    video_data.title.split('#')[0].strip(),
                    video_data.account
                )

                if existing_records:
                    # 更新现有记录
                    record_id = existing_records[0]['record_id']
                    await self.update_record(record_id, video_data)
                    return True, f"更新记录: {video_data.title} ({video_data.account})"
                else:
                    return False, f"查找不到对应记录: {video_data.title} ({video_data.account})"
            except FeishuAPIError as e:
                retry_count += 1
                if retry_count <= self.max_retries:
                    self.logger.warning(f"同步失败，{self.retry_delay}秒后重试 ({retry_count}/{self.max_retries}): {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    self.logger.error(f"同步最终失败: {str(e)}")
                    return False, f"同步失败: {str(e)}"
            
            except Exception as e:
                self.logger.error(f"同步异常: {str(e)}")
                return False, f"同步异常: {str(e)}"
        
        return False, "同步失败: 超过最大重试次数"
    
    async def batch_sync_video_data(self, video_data_list: List[YouTubeVideoData], 
                                   progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        批量同步视频数据
        
        Args:
            video_data_list: 视频数据列表
            progress_callback: 进度回调函数
            
        Returns:
            Dict[str, Any]: 同步结果统计
        """
        total_count = len(video_data_list)
        success_count = 0
        failed_count = 0
        created_count = 0
        updated_count = 0
        failed_items = []
        
        self.logger.info(f"开始批量同步 {total_count} 个视频数据")
        
        for i, video_data in enumerate(video_data_list):
            try:
                # 更新进度
                if progress_callback:
                    progress_callback(i + 1, total_count, video_data.title)
                
                # 同步数据
                success, message = await self.sync_video_data(video_data)
                
                if success:
                    success_count += 1
                    if "创建" in message:
                        created_count += 1
                    else:
                        updated_count += 1
                    self.logger.debug(f"同步成功 ({i+1}/{total_count}): {message}")
                else:
                    failed_count += 1
                    failed_items.append({
                        'title': video_data.title,
                        'error': message
                    })
                    self.logger.warning(f"同步失败 ({i+1}/{total_count}): {message}")
                
            except Exception as e:
                failed_count += 1
                error_msg = f"同步异常: {str(e)}"
                failed_items.append({
                    'title': video_data.title,
                    'error': error_msg
                })
                self.logger.error(f"同步异常 ({i+1}/{total_count}): {error_msg}")
        
        result = {
            'total_count': total_count,
            'success_count': success_count,
            'failed_count': failed_count,
            'created_count': created_count,
            'updated_count': updated_count,
            'failed_items': failed_items
        }
        
        self.logger.info(f"批量同步完成: 总计{total_count}, 成功{success_count}, 失败{failed_count}, 创建{created_count}, 更新{updated_count}")
        
        return result
    
    def test_connection(self) -> Tuple[bool, str]:
        """
        测试连接
        
        Returns:
            Tuple[bool, str]: (是否连接成功, 结果消息)
        """
        try:
            # 测试获取访问令牌
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                token = loop.run_until_complete(self.get_access_token())
                if token:
                    return True, "飞书API连接测试成功"
                else:
                    return False, "无法获取访问令牌"
            finally:
                loop.close()
                
        except Exception as e:
            return False, f"连接测试失败: {str(e)}"
