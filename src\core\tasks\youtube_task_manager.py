"""
YouTube抓取任务管理器

负责管理和调度YouTube数据抓取任务，包括：
1. 任务队列管理
2. 并发控制
3. 任务状态监控
4. 错误处理和重试
5. 结果收集
"""

import asyncio
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor
import logging

try:
    from ..scrapers.youtube_scraper import YouTube<PERSON>craper, YouTubeScrapingTask, YouTubeVideoData
    from ..browser.factory import BrowserManagerFactory
    from ..apis.feishu_client import FeishuClient
    from ...models.profile import Profile, ProfileManager
    from ...utils.logger import get_logger, TaskLogger
except ImportError:
    # 处理测试环境的导入
    from core.scrapers.youtube_scraper import YouTubeScraper, YouTubeScrapingTask, YouTubeVideoData
    from core.browser.factory import BrowserManagerFactory
    from core.apis.feishu_client import FeishuClient
    from models.profile import Profile, ProfileManager
    from utils.logger import get_logger, TaskLogger


class YouTubeTaskManager:
    """YouTube抓取任务管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化任务管理器
        
        Args:
            config: 配置信息
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        
        # 任务配置
        self.max_concurrent_tasks = config.get('max_concurrent_tasks', 3)
        self.task_timeout = config.get('task_timeout', 1800)  # 30分钟
        self.retry_count = config.get('retry_count', 3)
        self.retry_delay = config.get('retry_delay', 60)  # 1分钟
        
        # 任务状态
        self.tasks: Dict[str, YouTubeScrapingTask] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_results: Dict[str, List[YouTubeVideoData]] = {}
        self.task_errors: Dict[str, str] = {}
        
        # 控制变量
        self.is_running = False
        self.stop_requested = False
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.completion_callback: Optional[Callable] = None
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent_tasks)

        # 飞书客户端
        self.feishu_client = None
        feishu_config = config.get('feishu', {})
        if feishu_config and feishu_config.get('app_id') and feishu_config.get('app_secret'):
            try:
                # 使用内容表格ID
                feishu_config['table_id'] = feishu_config.get('table_id_content', '')
                self.feishu_client = FeishuClient(feishu_config)
                self.logger.info("飞书客户端初始化成功")
            except Exception as e:
                self.logger.warning(f"飞书客户端初始化失败: {str(e)}")
        else:
            self.logger.info("飞书配置不完整，跳过飞书同步功能")

        self.logger.info("YouTube任务管理器初始化完成")
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_completion_callback(self, callback: Callable):
        """设置完成回调函数"""
        self.completion_callback = callback
    
    async def start_scraping_tasks(self, profiles: List[Profile]) -> bool:
        """
        启动抓取任务
        
        Args:
            profiles: 要抓取的Profile列表
            
        Returns:
            bool: 是否启动成功
        """
        if self.is_running:
            self.logger.warning("任务管理器已在运行中")
            return False
        
        try:
            self.is_running = True
            self.stop_requested = False
            
            # 清理之前的状态
            self.tasks.clear()
            self.running_tasks.clear()
            self.task_results.clear()
            self.task_errors.clear()
            
            # 创建任务
            await self._create_tasks(profiles)
            
            # 启动任务执行
            await self._execute_tasks()
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动抓取任务失败: {str(e)}")
            self.is_running = False
            return False
    
    def stop_scraping_tasks(self):
        """停止抓取任务"""
        self.logger.info("请求停止抓取任务")
        self.stop_requested = True
        
        # 取消正在运行的任务
        for task_id, task in self.running_tasks.items():
            if not task.done():
                task.cancel()
                self.logger.info(f"取消任务: {task_id}")
    
    def get_task_status(self) -> Dict[str, Any]:
        """获取任务状态"""
        total_tasks = len(self.tasks)
        completed_tasks = len([t for t in self.tasks.values() if t.status == "completed"])
        failed_tasks = len([t for t in self.tasks.values() if t.status == "failed"])
        running_tasks = len([t for t in self.tasks.values() if t.status == "running"])
        
        return {
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks,
            'failed_tasks': failed_tasks,
            'running_tasks': running_tasks,
            'is_running': self.is_running,
            'stop_requested': self.stop_requested
        }
    
    def get_task_details(self) -> List[Dict[str, Any]]:
        """获取任务详细信息"""
        details = []
        
        for task_id, task in self.tasks.items():
            detail = {
                'task_id': task_id,
                'profile_name': task.profile.name,
                'status': task.status,
                'progress': task.progress,
                'message': task.message,
                'start_time': task.start_time.isoformat() if task.start_time else None,
                'end_time': task.end_time.isoformat() if task.end_time else None,
                'duration': str(task.get_duration()) if task.get_duration() else None,
                'result_count': len(task.result) if task.result else 0,
                'error': task.error
            }
            details.append(detail)
        
        return details
    
    def get_all_results(self) -> Dict[str, List[YouTubeVideoData]]:
        """获取所有任务结果"""
        results = {}
        
        for task_id, task in self.tasks.items():
            if task.result:
                results[task_id] = task.result
        
        return results
    
    async def _create_tasks(self, profiles: List[Profile]):
        """创建抓取任务"""
        self.logger.info(f"创建 {len(profiles)} 个抓取任务")
        
        for profile in profiles:
            try:
                # 创建浏览器管理器
                browser_config = self.config.get('browser', {}).get(profile.browser_type, {})
                browser_manager = BrowserManagerFactory.create_manager(
                    profile.browser_type, 
                    browser_config
                )
                
                if not browser_manager:
                    self.logger.error(f"无法创建浏览器管理器: {profile.browser_type}")
                    continue
                
                # 创建抓取器配置，包含YouTube数据提取配置
                scraper_config = self.config.get('scraping', {})
                # 添加YouTube数据提取配置
                youtube_config = self.config.get('youtube', {})
                if 'data_extraction' in youtube_config:
                    scraper_config['data_extraction'] = youtube_config['data_extraction']

                scraper = YouTubeScraper(browser_manager, scraper_config)
                
                # 创建任务
                task = YouTubeScrapingTask(profile, scraper)
                task_id = f"task-{profile.id}"
                self.tasks[task_id] = task
                
                self.logger.info(f"创建任务成功: {task_id} ({profile.name})")
                
            except Exception as e:
                self.logger.error(f"创建任务失败 {profile.name}: {str(e)}")
    
    async def _execute_tasks(self):
        """执行任务"""
        self.logger.info(f"开始执行 {len(self.tasks)} 个任务，最大并发数: {self.max_concurrent_tasks}")
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        
        # 创建任务协程
        task_coroutines = []
        for task_id, task in self.tasks.items():
            coroutine = self._execute_single_task(task_id, task, semaphore)
            task_coroutines.append(coroutine)
        
        # 等待所有任务完成
        try:
            await asyncio.gather(*task_coroutines, return_exceptions=True)
        except Exception as e:
            self.logger.error(f"任务执行过程中出错: {str(e)}")
        
        # 更新状态
        self.is_running = False
        
        # 调用完成回调
        if self.completion_callback:
            try:
                self.completion_callback(self.get_task_status())
            except Exception as e:
                self.logger.error(f"完成回调执行失败: {str(e)}")
        
        self.logger.info("所有任务执行完成")
    
    async def _execute_single_task(self, task_id: str, task: YouTubeScrapingTask, semaphore: asyncio.Semaphore):
        """执行单个任务"""
        async with semaphore:
            if self.stop_requested:
                task.status = "cancelled"
                task.message = "任务被取消"
                return
            
            retry_count = 0
            
            while retry_count <= self.retry_count:
                try:
                    self.logger.info(f"开始执行任务: {task_id} (尝试 {retry_count + 1}/{self.retry_count + 1})")
                    
                    # 包装进度回调
                    def progress_callback(task_obj, message):
                        if self.progress_callback:
                            try:
                                self.progress_callback(task_id, task_obj, message)
                            except Exception as e:
                                self.logger.error(f"进度回调执行失败: {str(e)}")
                    
                    # 执行任务
                    result = await asyncio.wait_for(
                        task.execute(progress_callback),
                        timeout=self.task_timeout
                    )
                    
                    self.task_results[task_id] = result
                    self.logger.info(f"任务执行成功: {task_id}, 抓取到 {len(result)} 个视频")

                    # 执行飞书同步
                    await self._sync_to_feishu(task_id, result)

                    break
                    
                except asyncio.TimeoutError:
                    retry_count += 1
                    error_msg = f"任务超时: {task_id}"
                    self.logger.warning(error_msg)
                    
                    if retry_count <= self.retry_count:
                        self.logger.info(f"等待 {self.retry_delay} 秒后重试...")
                        await asyncio.sleep(self.retry_delay)
                    else:
                        task.status = "failed"
                        task.error = "任务超时"
                        task.message = "任务执行超时"
                        self.task_errors[task_id] = error_msg
                
                except asyncio.CancelledError:
                    task.status = "cancelled"
                    task.message = "任务被取消"
                    self.logger.info(f"任务被取消: {task_id}")
                    break
                
                except Exception as e:
                    retry_count += 1
                    error_msg = f"任务执行失败: {task_id}, 错误: {str(e)}"
                    self.logger.error(error_msg)
                    
                    if retry_count <= self.retry_count:
                        self.logger.info(f"等待 {self.retry_delay} 秒后重试...")
                        await asyncio.sleep(self.retry_delay)
                    else:
                        task.status = "failed"
                        task.error = str(e)
                        task.message = f"任务执行失败: {str(e)}"
                        self.task_errors[task_id] = error_msg
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("清理任务管理器资源")
        
        # 停止任务
        self.stop_scraping_tasks()
        
        # 关闭线程池
        if self.executor:
            self.executor.shutdown(wait=True)
        
        # 清理状态
        self.tasks.clear()
        self.running_tasks.clear()
        self.task_results.clear()
        self.task_errors.clear()
        
        self.is_running = False

    async def _sync_to_feishu(self, task_id: str, video_data_list: List[YouTubeVideoData]):
        """
        同步数据到飞书

        Args:
            task_id: 任务ID
            video_data_list: 视频数据列表
        """
        if not self.feishu_client or not video_data_list:
            self.logger.debug(f"跳过飞书同步: task_id={task_id}, feishu_client={self.feishu_client is not None}, data_count={len(video_data_list) if video_data_list else 0}")
            return

        try:
            self.logger.info(f"开始同步 {len(video_data_list)} 个视频数据到飞书: {task_id}")

            # 定义进度回调
            def progress_callback(current: int, total: int, title: str):
                if self.progress_callback:
                    try:
                        progress_msg = f"飞书同步进度: {current}/{total} - {title}"
                        # 这里需要获取对应的任务对象，暂时使用task_id
                        self.progress_callback(task_id, None, progress_msg)
                    except Exception as e:
                        self.logger.error(f"飞书同步进度回调失败: {str(e)}")

            # 执行批量同步
            sync_result = await self.feishu_client.batch_sync_video_data(
                video_data_list,
                progress_callback
            )

            # 记录同步结果
            self.logger.info(f"飞书同步完成: {task_id}")
            self.logger.info(f"同步统计: 总计={sync_result['total_count']}, 成功={sync_result['success_count']}, 失败={sync_result['failed_count']}")
            self.logger.info(f"操作统计: 创建={sync_result['created_count']}, 更新={sync_result['updated_count']}")

            if sync_result['failed_count'] > 0:
                self.logger.warning(f"部分数据同步失败: {sync_result['failed_items']}")

        except Exception as e:
            self.logger.error(f"飞书同步异常: {task_id}, 错误: {str(e)}")
            # 不抛出异常，避免影响主任务流程
